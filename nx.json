{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "deploy": {"dependsOn": ["build"]}, "bundle": {"dependsOn": ["build"]}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/eslint.config.js"], "cache": true}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/src/test-setup.[jt]s"], "sharedGlobals": []}, "cli": {"packageManager": "pnpm"}, "generators": {"@nx/angular:application": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest", "e2eTestRunner": "cypress", "inlineStyle": "true", "inlineTemplate": "true", "prefix": "s2a", "standalone": "true", "strict": "true", "tags": "type:app"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest", "displayBlock": "true", "flat": "true", "inlineStyle": "true", "inlineTemplate": "true", "prefix": "s2a", "standalone": "true", "strict": "true", "style": "scss", "simpleName": "true"}, "@nx/angular:component": {"prefix": "s2a", "style": "scss", "changeDetection": "OnPush", "displayBlock": "true", "flat": "true", "inlineStyle": "true", "inlineTemplate": "true", "standalone": "true"}, "@nx/nest:library": {"controller": "true", "strict": true}}, "pluginsConfig": {"@nx/js": {"analyzeSourceFiles": true}}, "defaultProject": "web", "parallel": 8, "cacheDirectory": "node_modules/.cache/nx", "defaultBase": "test", "useLegacyCache": false}