import { Duration, RemovalPolicy } from 'aws-cdk-lib';
import { LogGroup, RetentionDays } from 'aws-cdk-lib/aws-logs';
import {
  IChainable,
  LogLevel,
  StateMachine,
  StateMachineType,
  Choice,
  Pass,
  Condition,
  JsonPath,
  DefinitionBody,
  Map,
} from 'aws-cdk-lib/aws-stepfunctions';
import { Construct } from 'constructs';

// This step function serves in enrichment stage of shift-service-event-bridge-pipe.
// It transforms the dynamodb stream type data to json data.

const newImagePresent = (): Condition => {
  const hasNewImageCondition = Condition.isPresent('$.dynamodb.NewImage');
  return hasNewImageCondition;
};

const oldImagePresent = (): Condition => {
  const hasOldImageCondition = Condition.isPresent('$.dynamodb.OldImage');
  return hasOldImageCondition;
};

const activeTemplateCondition = (): Condition => {
  const eventTypeCondition = Condition.stringEquals(
    '$.dynamodb.NewImage.entityType.S',
    'activeTemplate',
  );
  return Condition.and(newImagePresent(), eventTypeCondition);
};

const newInstanceCondition = (): Condition => {
  const eventTypeCondition = Condition.stringEquals(
    '$.dynamodb.NewImage.entityType.S',
    'instance',
  );
  return Condition.and(newImagePresent(), eventTypeCondition);
};

const oldInstanceCondition = (): Condition => {
  const eventTypeCondition = Condition.stringEquals(
    '$.dynamodb.OldImage.entityType.S',
    'instance',
  );
  return Condition.and(oldImagePresent(), eventTypeCondition);
};

const bothInstanceCondition = (): Condition => {
  const eventTypeCondition = Condition.stringEquals(
    '$.dynamodb.NewImage.entityType.S',
    'instance',
  );
  return Condition.and(
    oldImagePresent(),
    newImagePresent(),
    eventTypeCondition,
  );
};
const templateCondition = (): Condition => {
  const eventTypeCondition = Condition.stringEquals(
    '$.dynamodb.NewImage.entityType.S',
    'template',
  );

  return Condition.and(newImagePresent(), eventTypeCondition);
};

const activeTemplateState = (stack: Construct): Pass => {
  const activeState = new Pass(stack, 'activeTemplateState', {
    parameters: {
      eventName: JsonPath.stringAt('$.eventName'),
      eventSource: JsonPath.stringAt('$.eventSource'),
      dynamodb: {
        NewImage: {
          entityType: JsonPath.stringAt('$.dynamodb.NewImage.entityType.S'),
          SK: JsonPath.stringAt('$.dynamodb.NewImage.SK.S'),
          lineId: JsonPath.stringAt('$.dynamodb.NewImage.lineId.S'),
          PK: JsonPath.stringAt('$.dynamodb.NewImage.PK.S'),
          account: JsonPath.stringAt('$.dynamodb.NewImage.account.S'),
          fallbackTemplateId: JsonPath.stringAt(
            '$.dynamodb.NewImage.fallbackTemplateId.S',
          ),
          activeTemplateId: JsonPath.stringAt(
            '$.dynamodb.NewImage.activeTemplateId.S',
          ),
        },
      },
    },
  });
  return activeState;
};

const newInstanceState = (stack: Construct): Pass => {
  const instState = new Pass(stack, 'newInstanceState', {
    parameters: {
      eventName: JsonPath.stringAt('$.eventName'),
      eventSource: JsonPath.stringAt('$.eventSource'),
      dynamodb: { NewImage: getNewInstance() },
    },
  });
  return instState;
};

const oldInstanceState = (stack: Construct): Pass => {
  const instState = new Pass(stack, 'oldInstanceState', {
    parameters: {
      eventName: JsonPath.stringAt('$.eventName'),
      eventSource: JsonPath.stringAt('$.eventSource'),
      dynamodb: { OldImage: getOldInstance() },
    },
  });
  return instState;
};

const getOldInstance = () => {
  return {
    entityType: JsonPath.stringAt('$.dynamodb.OldImage.entityType.S'),
    SK: JsonPath.stringAt('$.dynamodb.OldImage.SK.S'),
    lineId: JsonPath.stringAt('$.dynamodb.OldImage.lineId.S'),
    PK: JsonPath.stringAt('$.dynamodb.OldImage.PK.S'),
    account: JsonPath.stringAt('$.dynamodb.OldImage.account.S'),
    start: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.OldImage.start.N)',
    ),
    end: JsonPath.numberAt('States.StringToJson($.dynamodb.OldImage.end.N)'),
    instanceId: JsonPath.stringAt('$.dynamodb.OldImage.instanceId.S'),
    createdAt: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.OldImage.createdAt.N)',
    ),
    updatedAt: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.OldImage.updatedAt.N)',
    ),
    name: JsonPath.stringAt('$.dynamodb.OldImage.name.S'),
    class: JsonPath.stringAt('$.dynamodb.OldImage.class.S'),
  };
};

const getNewInstance = () => {
  return {
    entityType: JsonPath.stringAt('$.dynamodb.NewImage.entityType.S'),
    SK: JsonPath.stringAt('$.dynamodb.NewImage.SK.S'),
    lineId: JsonPath.stringAt('$.dynamodb.NewImage.lineId.S'),
    PK: JsonPath.stringAt('$.dynamodb.NewImage.PK.S'),
    account: JsonPath.stringAt('$.dynamodb.NewImage.account.S'),
    start: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.NewImage.start.N)',
    ),
    end: JsonPath.numberAt('States.StringToJson($.dynamodb.NewImage.end.N)'),
    instanceId: JsonPath.stringAt('$.dynamodb.NewImage.instanceId.S'),
    createdAt: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.NewImage.createdAt.N)',
    ),
    updatedAt: JsonPath.numberAt(
      'States.StringToJson($.dynamodb.NewImage.updatedAt.N)',
    ),
    name: JsonPath.stringAt('$.dynamodb.NewImage.name.S'),
    class: JsonPath.stringAt('$.dynamodb.NewImage.class.S'),
  };
};

const instanceWithBothState = (stack: Construct): Pass => {
  const instState = new Pass(stack, 'instanceBothImageState', {
    parameters: {
      eventName: JsonPath.stringAt('$.eventName'),
      eventSource: JsonPath.stringAt('$.eventSource'),
      dynamodb: {
        NewImage: getNewInstance(),
        OldImage: getOldInstance(),
      },
    },
  });
  return instState;
};

// Helper function to check if qualificationId is present
const qualificationIdPresent = (): Condition => {
  return Condition.isPresent('$.qualificationId');
};

// Create processor state for items with qualificationId
const createProcessorWithQualificationId = (stack: Construct): Pass => {
  return new Pass(stack, 'ProcessorItemWithQualificationId', {
    parameters: {
      start: JsonPath.numberAt('States.StringToJson($.start.N)'),
      name: JsonPath.stringAt('$.name.S'),
      end: JsonPath.numberAt('States.StringToJson($.end.N)'),
      class: JsonPath.stringAt('$.class.S'),
      qualificationId: JsonPath.stringAt('$.qualificationId.S'),
    },
  });
};

// Create processor state for items without qualificationId
const createProcessorWithoutQualificationId = (stack: Construct): Pass => {
  return new Pass(stack, 'ProcessorItemWithoutQualificationId', {
    parameters: {
      start: JsonPath.numberAt('States.StringToJson($.start.N)'),
      name: JsonPath.stringAt('$.name.S'),
      end: JsonPath.numberAt('States.StringToJson($.end.N)'),
      class: JsonPath.stringAt('$.class.S'),
    },
  });
};

// Create choice state for handling optional qualificationId
const createQualificationIdChoice = (stack: Construct): IChainable => {
  const choiceState = new Choice(stack, 'CheckQualificationId');

  const processorWithQualificationId =
    createProcessorWithQualificationId(stack);
  const processorWithoutQualificationId =
    createProcessorWithoutQualificationId(stack);

  choiceState
    .when(qualificationIdPresent(), processorWithQualificationId)
    .otherwise(processorWithoutQualificationId);

  return choiceState;
};

const templateState = (stack: Construct): IChainable => {
  const tempState = new Pass(stack, 'Template', {
    parameters: {
      eventName: JsonPath.stringAt('$.eventName'),
      eventSource: JsonPath.stringAt('$.eventSource'),
      dynamodb: {
        NewImage: {
          createdAt: JsonPath.numberAt(
            'States.StringToJson($.dynamodb.NewImage.createdAt.N)',
          ),
          entityType: JsonPath.stringAt('$.dynamodb.NewImage.entityType.S'),
          shiftsPerDay: JsonPath.numberAt(
            'States.StringToJson($.dynamodb.NewImage.shiftsPerDay.N)',
          ),
          SK: JsonPath.stringAt('$.dynamodb.NewImage.SK.S'),
          name: JsonPath.stringAt('$.dynamodb.NewImage.name.S'),
          shiftStartTime: JsonPath.stringAt(
            '$.dynamodb.NewImage.shiftStartTime.S',
          ),
          PK: JsonPath.stringAt('$.dynamodb.NewImage.PK.S'),
          templateId: JsonPath.stringAt('$.dynamodb.NewImage.templateId.S'),
          type: JsonPath.stringAt('$.dynamodb.NewImage.type.S'),
          account: JsonPath.stringAt('$.dynamodb.NewImage.account.S'),
          updatedAt: JsonPath.numberAt(
            'States.StringToJson($.dynamodb.NewImage.updatedAt.N)',
          ),
          schedule: JsonPath.stringAt('$.dynamodb.NewImage.schedule.L[*][*]'),
        },
      },
    },
  });

  // Define Map state for schedule processing
  const scheduleMapState = new Map(stack, 'scheduleMapState', {
    itemsPath: JsonPath.stringAt('$.dynamodb.NewImage.schedule'),
    resultPath: JsonPath.stringAt('$.dynamodb.NewImage.schedule'),
    comment: 'Transform each record in Schedule array',
  });

  // Create the choice logic for handling optional qualificationId
  const qualificationIdChoice = createQualificationIdChoice(stack);

  // Add the choice state to the Map as the item processor
  scheduleMapState.itemProcessor(qualificationIdChoice);

  // Chain the template state with the map state
  tempState.next(scheduleMapState);

  return tempState;
};

const createStateMachineDefinition = (stack: Construct): Map => {
  const mapStateProcessEachRecord = new Map(stack, 'ProcessEachRecord', {
    itemsPath: JsonPath.stringAt('$'),
    resultPath: JsonPath.stringAt('$'),
    comment: 'Process each record in the input array',
  });
  const makeChoice = initChoice(stack);
  mapStateProcessEachRecord.itemProcessor(makeChoice);

  return mapStateProcessEachRecord;
};

const initChoice = (stack: Construct): IChainable => {
  // Define the states
  const choiceState = new Choice(stack, 'EntityTypeChoice');
  const failState = new Pass(stack, 'Fail');

  // Add the transitions for the choice state based on condition
  choiceState
    .when(bothInstanceCondition(), instanceWithBothState(stack))
    .when(activeTemplateCondition(), activeTemplateState(stack))
    .when(oldInstanceCondition(), oldInstanceState(stack))
    .when(newInstanceCondition(), newInstanceState(stack))
    .when(templateCondition(), templateState(stack))
    .otherwise(failState);

  // Return the starting state
  return choiceState;
};

export const transformer = (stack: Construct) => {
  const logGroup = new LogGroup(stack, 'Transformer', {
    retention: RetentionDays.ONE_MONTH,
    removalPolicy: RemovalPolicy.DESTROY,
    logGroupName: '/aws/vendedlogs/shift-service-shift-transformer',
  });

  const definition = createStateMachineDefinition(stack);

  return new StateMachine(stack, `TransformerStateMachine`, {
    stateMachineName: 's2a-shift-pipe-transformer',
    definitionBody: DefinitionBody.fromChainable(definition),
    timeout: Duration.seconds(29),
    stateMachineType: StateMachineType.EXPRESS,
    logs: {
      destination: logGroup,
      includeExecutionData: true,
      level: LogLevel.ALL,
    },
  });
};
