/* eslint-disable jest/expect-expect */

import { Stack } from 'aws-cdk-lib';
import { Match, Template } from 'aws-cdk-lib/assertions';

import { transformer } from './transformer-step-function';

describe('Transformer Function', () => {
  const stack = new Stack();
  const stateMachine = transformer(stack);
  const template = Template.fromStack(stack);

  test('Should create a StateMachine with correct properties for all scenarios', () => {
    expect(stateMachine).toBeDefined();
    expect(stateMachine.node.id).toBe('TransformerStateMachine');
  });

  test('stack has StateMachine', () => {
    template.hasResourceProperties('AWS::StepFunctions::StateMachine', {});
  });

  test('StateMachine has type EXPRESS', () => {
    template.hasResourceProperties('AWS::StepFunctions::StateMachine', {
      StateMachineType: 'EXPRESS',
    });
  });

  test('StateMachine has correct name', () => {
    template.hasResourceProperties('AWS::StepFunctions::StateMachine', {
      StateMachineName: 's2a-shift-pipe-transformer',
    });
  });

  test('StateMachine has correct timeout in definition', () => {
    const definitionString = template.findResources(
      'AWS::StepFunctions::StateMachine',
    );
    const stateMachineResource = Object.values(definitionString)[0] as Record<
      string,
      unknown
    >;
    const definition = (
      stateMachineResource['Properties'] as Record<string, unknown>
    )['DefinitionString'];

    expect(definition).toContain('"TimeoutSeconds":29');
  });

  test('StateMachine has logs (with ExecutionData and ALL Level)', () => {
    template.hasResourceProperties('AWS::StepFunctions::StateMachine', {
      LoggingConfiguration: {
        Destinations: [
          {
            CloudWatchLogsLogGroup: Match.anyValue(),
          },
        ],
        IncludeExecutionData: true,
        Level: 'ALL',
      },
    });
  });

  test('stack has CloudWatch Log Group for transformer', () => {
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      LogGroupName: '/aws/vendedlogs/shift-service-shift-transformer',
      RetentionInDays: 30,
    });
  });

  describe('StateMachine Definition Structure', () => {
    test('StateMachine definition includes main processing Map state', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as Record<
        string,
        unknown
      >;
      const definition = (
        stateMachineResource['Properties'] as Record<string, unknown>
      )['DefinitionString'];

      expect(definition).toContain('ProcessEachRecord');
      expect(definition).toContain('Process each record in the input array');
    });

    test('StateMachine definition includes entity type choice logic', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as any;
      const definition = stateMachineResource.Properties.DefinitionString;

      expect(definition).toContain('EntityTypeChoice');
      expect(definition).toContain('activeTemplate');
      expect(definition).toContain('instance');
      expect(definition).toContain('template');
    });

    test('StateMachine definition includes qualificationId handling', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as any;
      const definition = stateMachineResource.Properties.DefinitionString;

      expect(definition).toContain('CheckQualificationId');
      expect(definition).toContain('ProcessorItemWithQualificationId');
      expect(definition).toContain('ProcessorItemWithoutQualificationId');
    });

    test('StateMachine definition includes schedule processing Map state', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as any;
      const definition = stateMachineResource.Properties.DefinitionString;

      expect(definition).toContain('scheduleMapState');
      expect(definition).toContain('Transform each record in Schedule array');
    });

    test('StateMachine definition includes all entity-specific states', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as any;
      const definition = stateMachineResource.Properties.DefinitionString;

      // Check for all the specific state names
      expect(definition).toContain('activeTemplateState');
      expect(definition).toContain('newInstanceState');
      expect(definition).toContain('oldInstanceState');
      expect(definition).toContain('instanceBothImageState');
      expect(definition).toContain('Template');
    });

    test('StateMachine definition includes proper JSON path transformations', () => {
      const definitionString = template.findResources(
        'AWS::StepFunctions::StateMachine',
      );
      const stateMachineResource = Object.values(definitionString)[0] as any;
      const definition = stateMachineResource.Properties.DefinitionString;

      // Check for JSON path transformations used in the step function
      expect(definition).toContain('States.StringToJson');
      expect(definition).toContain('$.dynamodb.NewImage');
      expect(definition).toContain('$.dynamodb.OldImage');
      expect(definition).toContain('$.qualificationId');
    });
  });
});
