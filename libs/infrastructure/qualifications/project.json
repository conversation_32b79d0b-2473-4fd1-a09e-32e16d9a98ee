{"name": "infrastructure-qualifications", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/infrastructure/qualifications/src", "projectType": "library", "tags": ["type:infrastructure", "scope:infrastructure", "platform:backend"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/infrastructure/qualifications/jest.config.ts"}}}}