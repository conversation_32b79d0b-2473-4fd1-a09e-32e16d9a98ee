import { resolve } from 'path';

import {
  aws_scheduler as scheduler,
  aws_iam as iam,
  aws_secretsmanager as secretsmanager,
  aws_kms as KMS,
  Fn,
} from 'aws-cdk-lib';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';

import { LambdaConstruct } from '@shift-management/infrastructure/constructs';
import { SECRETS_MANAGER_KMS_ALIAS_NAME } from '@shift-management/infrastructure/util';

/**
 * Creates a daily scheduled Lambda function that runs at 12 AM UTC
 * @param stack - CDK construct scope
 * @param shiftData - DynamoDB table for shift data
 * @param stage - Deployment stage
 */
export const addQualificationScheduledLambda = (
  stack: Construct,
  shiftData: ITable,
  stage: string,
) => {
  // Path to the existing qualification cron job handler
  const dailyScheduledDistPath = resolve(
    './dist/apps/backend/qualification-cron-job',
  );

  // Get the KMS key for Secrets Manager encryption
  const encryptionKey: KMS.IKey = KMS.Alias.fromAliasName(
    stack,
    'kms-secrets-manager-alias',
    Fn.importValue(SECRETS_MANAGER_KMS_ALIAS_NAME),
  );

  // Create Secrets Manager secret for third-party API credentials
  // Note: You need to manually set the secret value in AWS console with JSON format:
  // {"client_id": "your_client_id", "client_secret": "your_client_secret"}
  const apiCredentialsSecret = new secretsmanager.Secret(
    stack,
    'QualificationApiCredentials',
    {
      secretName: `shift-service-qualification-api-credentials-${stage}`,
      description:
        'Client ID and Client Secret for third-party qualification API - manually set values in AWS console',
      encryptionKey: encryptionKey,
    },
  );

  // Environment variables for the Lambda function
  const dailyScheduledEnv = {
    SHIFT_DATA_TABLE_NAME: shiftData.tableName,
    SECRET_NAME: apiCredentialsSecret.secretName,
    QUALIFICATION_API_URL:
      'https://oem-services-qpsffewd.it-cpi024-rt.cfapps.eu10-002.hana.ondemand.com/http/Datalayer/Qualifications/texts',
    TOKEN_URL:
      'https://oem-services-qpsffewd.authentication.eu10.hana.ondemand.com/oauth/token',
    LOG_LEVEL: 'WARN',
  };

  // Create Lambda function using LambdaConstruct
  const dailyScheduledLambda = new LambdaConstruct(
    stack,
    'shift-service-qualification-scheduler-lambda',
    'main.handler',
    dailyScheduledDistPath,
    dailyScheduledEnv,
    dailyScheduledDistPath,
    stage,
    'shift-service-qualification-scheduler',
    2048, // memory size in MB
    300, // timeout in seconds (5 minutes)
  );

  // Grant permissions to access DynamoDB table
  shiftData.grantReadWriteData(dailyScheduledLambda.lambdaFunction);

  // Grant permission to read from Secrets Manager
  apiCredentialsSecret.grantRead(dailyScheduledLambda.lambdaFunction);

  // IAM Role for EventBridge Scheduler to invoke the Lambda
  const schedulerRole = new iam.Role(
    stack,
    'QualificationLambdaSchedulerRole',
    {
      assumedBy: new iam.ServicePrincipal('scheduler.amazonaws.com'),
      description:
        'IAM Role for EventBridge Scheduler to invoke daily scheduled Lambda',
    },
  );

  // Grant the scheduler role permission to invoke the Lambda
  dailyScheduledLambda.lambdaFunction.grantInvoke(schedulerRole);

  // EventBridge Scheduler to trigger Lambda daily at 12 AM UTC
  new scheduler.CfnSchedule(stack, 'QualificationSchedulerLambda', {
    flexibleTimeWindow: { mode: 'OFF' },
    scheduleExpression: 'cron(0,30 * * * ? *)', // Every 30 minutes
    target: {
      arn: dailyScheduledLambda.lambdaFunction.functionArn,
      roleArn: schedulerRole.roleArn,
    },
    name: 'shift-service-qualification-scheduler-lambda-trigger',
    description:
      'Trigger qualification scheduled Lambda function every 30 minutes',
  });
};
