import { Stack } from 'aws-cdk-lib';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';

import { addQualificationsHandler } from './add-qualifications-handler';

// Mocking dependencies
jest.mock('path', () => {
  const originalModule = jest.requireActual('path');
  return {
    ...originalModule,
    resolve: jest.fn(() => '/mocked/path'),
  } as Record<string, unknown>;
});

// Mock LambdaConstruct to avoid asset loading
const mockLambdaFunction = {
  grantInvoke: jest.fn(),
};

jest.mock('@shift-management/infrastructure/constructs', () => {
  return {
    LambdaConstruct: jest.fn().mockImplementation(() => ({
      lambdaFunction: mockLambdaFunction,
    })),
  };
});

const mockGrantReadData = jest.fn();
const mockGrantReadWriteData = jest.fn();

const mockTable: ITable = {
  tableName: 'mockTable',
  grantReadData: mockGrantReadData,
  grantReadWriteData: mockGrantReadWriteData,
} as unknown as ITable;

describe('addQualificationsHandler', () => {
  let stack: Stack;

  beforeEach(() => {
    stack = new Stack();
    mockGrantReadData.mockClear();
    mockGrantReadWriteData.mockClear();
    mockLambdaFunction.grantInvoke.mockClear();
  });

  it('creates a Lambda function and grants read access to the table', () => {
    const lambdaFunction = addQualificationsHandler(stack, mockTable, 'dev');
    expect(lambdaFunction).toBe(mockLambdaFunction);
    expect(mockGrantReadData).toHaveBeenCalledWith(mockLambdaFunction);
  });
});
