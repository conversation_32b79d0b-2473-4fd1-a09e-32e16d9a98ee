import { Stack } from 'aws-cdk-lib';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';

import { addQualificationScheduledLambda } from './qualification-scheduler-lambda';

// Mocking dependencies
jest.mock('path', () => {
  const originalModule = jest.requireActual('path');
  return {
    ...originalModule,
    resolve: jest.fn(() => '/mocked/path'),
  } as Record<string, unknown>;
});

// Mock LambdaConstruct to avoid asset loading
const mockLambdaFunction = {
  grantInvoke: jest.fn(),
  functionArn: 'mock-function-arn',
};

const mockLambdaConstruct = jest.fn().mockImplementation(() => ({
  lambdaFunction: mockLambdaFunction,
}));

const mockCfnSchedule = jest.fn();

jest.mock('@shift-management/infrastructure/constructs', () => ({
  LambdaConstruct: mockLambdaConstruct,
}));

// Mock aws-cdk-lib imports
jest.mock('aws-cdk-lib', () => {
  const actual = jest.requireActual('aws-cdk-lib');
  return {
    ...actual,
    Fn: {
      importValue: jest.fn().mockReturnValue('mock-kms-key'),
    },
    aws_secretsmanager: {
      Secret: jest.fn().mockImplementation(() => ({
        secretName: 'mock-secret-name',
        grantRead: jest.fn(),
      })),
    },
    aws_iam: {
      Role: jest.fn().mockImplementation(() => ({
        roleArn: 'mock-role-arn',
      })),
      ServicePrincipal: jest.fn(),
    },
    aws_scheduler: {
      CfnSchedule: mockCfnSchedule,
    },
    aws_kms: {
      Alias: {
        fromAliasName: jest.fn().mockReturnValue({
          keyId: 'mock-key-id',
        }),
      },
      IKey: {},
    },
  } as Record<string, unknown>;
});

const mockGrantReadWriteData = jest.fn();

const mockTable: ITable = {
  tableName: 'mockTable',
  grantReadWriteData: mockGrantReadWriteData,
} as unknown as ITable;

describe('addQualificationScheduledLambda', () => {
  let stack: Stack;

  beforeEach(() => {
    stack = new Stack();
    mockGrantReadWriteData.mockClear();
    mockLambdaFunction.grantInvoke.mockClear();
    mockLambdaConstruct.mockClear();
    mockCfnSchedule.mockClear();
    jest.clearAllMocks();
  });

  it('creates a Lambda function with correct configuration', () => {
    addQualificationScheduledLambda(stack, mockTable, 'dev');

    // Verify LambdaConstruct was called with correct parameters
    expect(mockLambdaConstruct).toHaveBeenCalledWith(
      stack,
      'shift-service-qualification-scheduler-lambda',
      'main.handler',
      '/mocked/path',
      expect.objectContaining({
        SHIFT_DATA_TABLE_NAME: 'mockTable',
        SECRET_NAME: expect.any(String),
        QUALIFICATION_API_URL: expect.any(String),
        TOKEN_URL: expect.any(String),
        LOG_LEVEL: 'WARN',
      }),
      '/mocked/path',
      'dev',
      'shift-service-qualification-scheduler',
      2048,
      300,
    );
  });

  it('grants read/write permissions to the DynamoDB table', () => {
    addQualificationScheduledLambda(stack, mockTable, 'dev');
    expect(mockGrantReadWriteData).toHaveBeenCalledWith(mockLambdaFunction);
  });

  it('creates a scheduler with the correct configuration', () => {
    addQualificationScheduledLambda(stack, mockTable, 'dev');

    expect(mockCfnSchedule).toHaveBeenCalledWith(
      stack,
      'QualificationSchedulerLambda',
      expect.objectContaining({
        flexibleTimeWindow: { mode: 'OFF' },
        scheduleExpression: 'cron(0 0 * * ? *)',
        target: expect.objectContaining({
          arn: 'mock-function-arn',
          roleArn: 'mock-role-arn',
        }),
        name: 'shift-service-qualification-scheduler-lambda-trigger',
        description: expect.any(String),
      }),
    );
  });

  it('grants invoke permission to the scheduler role', () => {
    addQualificationScheduledLambda(stack, mockTable, 'dev');
    expect(mockLambdaFunction.grantInvoke).toHaveBeenCalled();
  });
});
