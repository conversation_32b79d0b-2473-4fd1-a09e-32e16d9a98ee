export const SHIFTS_API_STACK = 's2a-shift-service';

export const SHIFT_TABLE_NAME = 'shift-data';

export const SHIFT_BUS_ARN_EXPORT = SHIFTS_API_STACK + '-shiftEventBusArn';
export const REST_API_RESOURCE_ID_EXPORT =
  SHIFTS_API_STACK + '-restApiRootResourceId';
export const REST_API_ID_EXPORT = SHIFTS_API_STACK + '-restApiId';

export const SHIFT_SCHEDULER_ROLE_NAME = 'ShiftServiceSchedulerRole';
export const SHIFT_SCHEDULER_GROUP_NAME = 'shift-service-schedule-group';
export const SHIFT_SCHEDULER_TARGET_QUEUE_NAME =
  'shift-service-instance-scheduler-target-sqs-queue';
export const DYNAMODB_KMS_ALIAS_NAME = 'shift-service-dynamodb-kms-key-alias';
export const SQS_KMS_ALIAS_NAME = 'shift-service-sqs-kms-key-alias';
export const EVENTBRIDGE_KMS_ALIAS_NAME = 'shift-service-eb-kms-key-alias';
export const SECRETS_MANAGER_KMS_ALIAS_NAME =
  'shift-service-secrets-manager-kms-key-alias';
