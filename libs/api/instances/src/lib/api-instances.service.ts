import { Logger } from '@aws-lambda-powertools/logger';
import {
  DeleteCommand,
  DeleteCommandInput,
  PutCommandInput,
  QueryCommandInput,
  UpdateCommandInput,
} from '@aws-sdk/lib-dynamodb';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { addYears, getTime, milliseconds } from 'date-fns';
import { v4 as UUIDV4 } from 'uuid';

import {
  ComparisonOperator,
  DynamoDBClientProvider,
  getPrimaryKeys,
  NotFoundError,
  NoUpdateParamsError,
} from '@shift-management/api/util/dynamodb-client';

import {
  CreateInstanceDTO,
  Instance,
  InstanceDB,
  UpdateInstanceDTO,
} from './instance.entity';

@Injectable()
export class ApiInstancesService {
  private readonly logger: Logger = new Logger({
    serviceName: 'ApiInstancesService',
  });
  private tableName =
    process.env['SHIFT_DATA_TABLE_NAME'] || 's2a-shift-service-shift-data';

  constructor(private dynamodbClient: DynamoDBClientProvider) {}

  async findAllInstancesForLineInTimeRange(
    account: string,
    lineId: string,
    startTime: number,
    endTime: number,
  ): Promise<InstanceDB[]> {
    if (account === undefined || lineId === undefined) {
      throw Error(
        'findAllInstancesForLineInTimeRange: account and lineId are required',
      );
    }
    const cmdInput: QueryCommandInput = {
      IndexName: 'Instances',
      TableName: this.tableName,
      KeyConditionExpression:
        'PK = :partitionKey AND LSI1_SK BETWEEN :start AND :end',
      ExpressionAttributeValues: {
        ':partitionKey': this.getPartitionKey(account, lineId),
        ':start': startTime,
        ':end': endTime,
      },
      ConsistentRead: true,
    };
    return await this.dynamodbClient.queryAll<InstanceDB>(cmdInput);
  }

  async findAllInstancesForLine(
    account: string,
    lineId: string,
  ): Promise<InstanceDB[]> {
    if (account === undefined || lineId === undefined) {
      throw Error('findAllInstancesForLine: account and lineId are required');
    }
    const cmdInput: QueryCommandInput = {
      TableName: this.tableName,
      KeyConditionExpression: 'PK = :partitionKey',
      ExpressionAttributeValues: {
        ':partitionKey': this.getPartitionKey(account, lineId),
      },
    };
    return await this.dynamodbClient.queryAll<InstanceDB>(cmdInput);
  }

  async storeInstances(
    account: string,
    lineId: string,
    instances: InstanceDB[],
  ): Promise<void> {
    this.logger.info(
      `Storing instances for account:${account} line:${lineId} with length:${instances.length}`,
    );
    await this.dynamodbClient.batchWrite(this.tableName, instances);
  }

  async insertInstanceForLine(
    lineId: string,
    account: string,
    instanceDTO: CreateInstanceDTO,
  ): Promise<Instance> {
    const id = UUIDV4();
    const instanceToBeStored: InstanceDB = {
      start: instanceDTO.start,
      end: instanceDTO.end,
      name: instanceDTO.name,
      class: instanceDTO.class,
      account: account,
      entityType: 'instance',
      instanceId: id,
      lineId: lineId,
      qualificationId: instanceDTO.qualificationId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      LSI1_SK: instanceDTO.start,
      PK: this.getPartitionKey(account, lineId),
      SK: this.getSortKey(id),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    const cmdInput: PutCommandInput = {
      TableName: this.tableName,
      Item: instanceToBeStored,
    };
    await this.dynamodbClient.insert<InstanceDB>(cmdInput);
    return Instance.from(instanceToBeStored);
  }

  async deleteInstanceForLine(
    lineId: string,
    instanceId: string,
    account: string,
  ): Promise<void> {
    const cmd: DeleteCommandInput = {
      TableName: this.tableName,
      Key: {
        PK: this.getPartitionKey(account, lineId),
        SK: this.getSortKey(instanceId),
      },
    };
    await this.dynamodbClient.documentClient.send(new DeleteCommand(cmd));
    return;
  }

  async deleteInstancesForLine(
    lineId: string,
    account: string,
    startTime?: number,
    endTime?: number,
  ): Promise<void> {
    let instances: InstanceDB[] = [];
    if (startTime === undefined) {
      instances = await this.findAllInstancesForLine(account, lineId);
    } else {
      if (endTime === undefined) {
        endTime = getTime(addYears(startTime, 100));
      }
      instances = await this.findAllInstancesForLineInTimeRange(
        account,
        lineId,
        startTime,
        endTime,
      );
    }

    await this.dynamodbClient.batchDelete(
      this.tableName,
      getPrimaryKeys(instances),
    );
  }

  async getLastInstance(
    lineId: string,
    account: string,
  ): Promise<Instance | undefined> {
    const cmdInput: QueryCommandInput = {
      IndexName: 'Instances',
      TableName: this.tableName,
      KeyConditionExpression: 'PK = :partitionKey',
      ExpressionAttributeValues: {
        ':partitionKey': this.getPartitionKey(account, lineId),
      },
      ScanIndexForward: false,
      Limit: 1,
      ConsistentRead: true,
    };
    const result = await this.dynamodbClient.documentClient.query(cmdInput);
    if (result.Items?.[0]) {
      const instanceDB = result.Items[0] as InstanceDB;
      this.logger.debug(`Received last instance`, { instanceDB });
      return Instance.from(instanceDB);
    }
    this.logger.debug(`No last instance found. Returning undefined.`);
    return undefined;
  }

  async updateInstance(
    lineId: string,
    instanceId: string,
    account: string,
    instance: UpdateInstanceDTO,
  ): Promise<Instance> {
    try {
      const now = Date.now();
      const updateInput = this.dynamodbClient.buildUpdateExpression(
        {
          ...instance,
          updatedAt: now,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          LSI1_SK: instance.start ? instance.start : undefined,
        },
        [
          {
            key: 'PK',
            value: this.getPartitionKey(account, lineId),
          },
          {
            key: 'updatedAt',
            value: now,
            operator: ComparisonOperator.LessThan,
          },
        ],
      );
      const cmdInput: UpdateCommandInput = {
        TableName: this.tableName,
        Key: {
          PK: this.getPartitionKey(account, lineId),
          SK: this.getSortKey(instanceId),
        },
        UpdateExpression: updateInput.UpdateExpression,
        ExpressionAttributeValues: updateInput.ExpressionAttributeValues,
        ExpressionAttributeNames: updateInput.ExpressionAttributeNames,
        ConditionExpression: updateInput.ConditionExpression,
      };
      return Instance.from(
        await this.dynamodbClient.updateItem<InstanceDB>(cmdInput),
      );
    } catch (err) {
      if (err instanceof NoUpdateParamsError) {
        throw new BadRequestException(
          'No update parameters provided. Please provide at least one of the following: start and end, name, class, or qualificationId.',
        );
      } else if (err instanceof NotFoundError) {
        throw new NotFoundException(
          `Instance with ${instanceId} does not exist.`,
        );
      } else {
        throw err;
      }
    }
  }

  async isOverlapping(
    start: number,
    end: number,
    lineId: string,
    account: string,
    instanceId: string | undefined = undefined,
  ) {
    const startTime = start - milliseconds({ days: 1 });
    const instancesInTimeRange = await this.findAllInstancesForLineInTimeRange(
      account,
      lineId,
      startTime,
      end,
    );
    const filterModifiedInstance = instancesInTimeRange.filter(
      (instance) => instance.instanceId !== instanceId,
    );

    for (const instance of filterModifiedInstance) {
      if (instance.start < end && start < instance.end) {
        return true;
      }
    }

    return false;
  }

  private getPartitionKey(account: string, lineId: string): string {
    return `A#${account}#L#${lineId}`;
  }
  private getSortKey(instanceId: string): string {
    return `I#${instanceId}`;
  }
}
