import {
  Controller,
  Get,
  Delete,
  Param,
  Query,
  Patch,
  Body,
  Post,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import {
  AuthorizerContext,
  InjectAuthorizerContext,
} from '@shift-management/api/util/authorizer-context';

import { ApiInstancesService } from './api-instances.service';
import { ApiMarkerInstanceService } from './api-marker-instance.service';
import {
  Instance,
  InstanceResponse,
  CreateInstanceDTO,
  UpdateInstanceDTO,
} from './instance.entity';

@ApiTags('instances')
@Controller('instances/line')
export class ApiInstancesController {
  constructor(
    private shiftBackendInstancesService: ApiInstancesService,
    private shiftBackendMarkerService: ApiMarkerInstanceService,
  ) {}

  @Get(':lineId?')
  async getInstancesForLine(
    @Param('lineId') lineId: string,
    @Query('start') start: number,
    @Query('end') end: number,
    @InjectAuthorizerContext() ctx: AuthorizerContext,
  ): Promise<InstanceResponse> {
    const instances =
      await this.shiftBackendInstancesService.findAllInstancesForLineInTimeRange(
        ctx.account,
        lineId,
        start,
        end,
      );
    return new InstanceResponse(instances);
  }

  @Post(':lineId/instance')
  async insertInstanceForLine(
    @Param('lineId') lineId: string,
    @Body() instance: CreateInstanceDTO,
    @InjectAuthorizerContext() ctx: AuthorizerContext,
  ) {
    if (
      await this.shiftBackendInstancesService.isOverlapping(
        instance.start,
        instance.end,
        lineId,
        ctx.account,
      )
    ) {
      throw new BadRequestException('Shift is overlapping.');
    }
    if (
      !(await this.shiftBackendMarkerService.isWithinValidEditTimeRange(
        ctx.account,
        lineId,
        instance.end,
      ))
    ) {
      throw new BadRequestException(
        'components.shift_management.error_dialog.instance_out_of_range_error',
      );
    }
    return this.shiftBackendInstancesService.insertInstanceForLine(
      lineId,
      ctx.account,
      instance,
    );
  }

  @Delete(':lineId/instance/:instanceId')
  deleteInstanceForLine(
    @Param('lineId') lineId: string,
    @Param('instanceId') instanceId: string,
    @InjectAuthorizerContext() ctx: AuthorizerContext,
  ) {
    return this.shiftBackendInstancesService.deleteInstanceForLine(
      lineId,
      instanceId,
      ctx.account,
    );
  }

  @Patch(':lineId/instance/:instanceId')
  async updateInstance(
    @Param('lineId') lineId: string,
    @Param('instanceId') instanceId: string,
    @Body() instance: UpdateInstanceDTO,
    @InjectAuthorizerContext() ctx: AuthorizerContext,
  ): Promise<Instance> {
    if (
      instance.start == null &&
      instance.end == null &&
      instance.class == null &&
      instance.name == null &&
      instance.qualificationId == null
    ) {
      throw new BadRequestException('No value was given.');
    }
    if (
      instance.start != null &&
      instance.end != null &&
      (await this.shiftBackendInstancesService.isOverlapping(
        instance.start,
        instance.end,
        lineId,
        ctx.account,
        instanceId,
      ))
    ) {
      throw new BadRequestException('Shift is overlapping.');
    }
    if (
      instance.end &&
      !(await this.shiftBackendMarkerService.isWithinValidEditTimeRange(
        ctx.account,
        lineId,
        instance.end,
      ))
    ) {
      throw new BadRequestException(
        'components.shift_management.error_dialog.instance_out_of_range_error',
      );
    }
    return this.shiftBackendInstancesService.updateInstance(
      lineId,
      instanceId,
      ctx.account,
      instance,
    );
  }
}
