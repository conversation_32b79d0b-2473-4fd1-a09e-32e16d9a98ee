// eslint-disable-next-line max-classes-per-file
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

import 'reflect-metadata';

import { ClassEnum } from '@shift-management/shared/types';

import {
  IsValidScheduleItemEnd,
  IsValidScheduleItemStart,
  IsScheduleItemShorterThan24Hours,
} from './api-templates.validation';

export interface TemplateDB {
  PK: string;
  SK: string;
  name: string;
  entityType: string;
  shiftsPerDay: number;
  shiftStartTime: string;
  type: TemplateType;
  schedule: ScheduleItem[];
  createdAt: number;
  updatedAt: number;
  account: string;
  templateId: string;
}

export interface BootstrapTemplate extends TemplateDB {
  lineId: string;
}

export enum TemplateType {
  UserDefined = 'userDefined',
  Predefined = 'predefined',
}

export class UpdateTemplate {
  @IsOptional()
  @IsString()
  name?: string;
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  schedule?: ScheduleItem[];
}

export class CreateTemplate {
  @IsNotEmpty()
  @IsInt()
  shiftsPerDay!: number;
  @IsNotEmpty()
  @IsString()
  shiftStartTime!: string;
  @IsNotEmpty()
  @IsString()
  name!: string;
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  schedule!: ScheduleItem[];
}

/**
 * Template Interface
 */
export class Template extends CreateTemplate {
  @IsUUID()
  @IsNotEmpty()
  id!: string;
  @IsNotEmpty()
  @IsString()
  account!: string;
  @IsEnum(TemplateType)
  type!: TemplateType;
  static from(template: TemplateDB): Template {
    const temp = new Template();
    temp.name = template.name;
    temp.type = template.type;
    temp.id = template.templateId;
    temp.account = template.account;
    temp.schedule = template.schedule;
    temp.shiftsPerDay = template.shiftsPerDay;
    temp.shiftStartTime = template.shiftStartTime;
    return temp;
  }
}

export class ScheduleItem {
  @IsEnum(ClassEnum)
  @IsNotEmpty()
  class!: ClassEnum;
  @IsInt()
  @IsNotEmpty()
  @IsValidScheduleItemStart()
  @IsScheduleItemShorterThan24Hours('end')
  start!: number;
  @IsInt()
  @IsNotEmpty()
  @IsValidScheduleItemEnd('start')
  end!: number;
  @IsNotEmpty()
  @IsString()
  name!: string;
  @IsOptional()
  @IsString()
  qualificationId?: string;
}

export class TemplatesResponse {
  templates: Template[];
  constructor(templates: TemplateDB[]) {
    this.templates = templates.map((template) => Template.from(template));
  }
}
