import { Logger } from '@aws-lambda-powertools/logger';
import { QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { Injectable } from '@nestjs/common';

import { DynamoDBClientProvider } from '@shift-management/api/util/dynamodb-client';

import { Qualification, QualificationDB } from './qualification.entity';

@Injectable()
export class ApiQualificationsService {
  private readonly logger: Logger = new Logger({
    serviceName: 'ApiQualificationsService',
  });
  private tableName =
    process.env['SHIFT_DATA_TABLE_NAME'] || 's2a-shift-service-shift-data';

  constructor(private dynamodbClient: DynamoDBClientProvider) {}

  /**
   * Get all qualifications for a specific account
   * @param account - The account name to get qualifications for
   * @returns Promise that resolves to array of qualifications
   */
  async getQualificationsForAccount(account: string): Promise<Qualification[]> {
    if (!account) {
      throw new Error('getQualificationsForAccount: account is required');
    }

    const cmdInput: QueryCommandInput = {
      TableName: this.tableName,
      KeyConditionExpression:
        'PK = :partitionKey AND begins_with(SK, :sortKeyPrefix)',
      ExpressionAttributeValues: {
        ':partitionKey': this.getPartitionKey(account),
        ':sortKeyPrefix': 'Q#',
      },
      ConsistentRead: true,
    };

    try {
      const plainObjects =
        await this.dynamodbClient.queryAll<QualificationDB>(cmdInput);

      this.logger.info(
        `Retrieved ${plainObjects.length} qualifications for account: ${account}`,
        { account, qualificationCount: plainObjects.length },
      );

      const qualifications = plainObjects.map((item) =>
        QualificationDB.from(item),
      );
      return qualifications.map((qentity) => qentity.toQualification());
    } catch (error) {
      this.logger.error('Error retrieving qualifications:', { error, account });
      throw error;
    }
  }

  private getPartitionKey(account: string): string {
    return `A#${account}`;
  }
}
