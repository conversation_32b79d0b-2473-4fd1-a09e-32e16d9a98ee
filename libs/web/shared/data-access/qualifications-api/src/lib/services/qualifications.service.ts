/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { QualificationsApiConfiguration } from '../qualifications-api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { getQualificationsForAccount } from '../fn/qualifications/get-qualifications-for-account';
import { GetQualificationsForAccount$Params } from '../fn/qualifications/get-qualifications-for-account';
import { QualificationResponse } from '../models/qualification-response';

@Injectable({ providedIn: 'root' })
export class QualificationsService extends BaseService {
  constructor(config: QualificationsApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getQualificationsForAccount()` */
  static readonly GetQualificationsForAccountPath = '/qualifications';

  /**
   * Get qualifications for the current account.
   *
   * Retrieves all qualifications associated with the current account from the authorizer context
   *
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getQualificationsForAccount()` instead.
   *
   * This method doesn't expect any request body.
   */
  getQualificationsForAccount$Response(
    params?: GetQualificationsForAccount$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<QualificationResponse>> {
    return getQualificationsForAccount(
      this.http,
      this.rootUrl,
      params,
      context,
    );
  }

  /**
   * Get qualifications for the current account.
   *
   * Retrieves all qualifications associated with the current account from the authorizer context
   *
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getQualificationsForAccount$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getQualificationsForAccount(
    params?: GetQualificationsForAccount$Params,
    context?: HttpContext,
  ): Observable<QualificationResponse> {
    return this.getQualificationsForAccount$Response(params, context).pipe(
      map(
        (r: StrictHttpResponse<QualificationResponse>): QualificationResponse =>
          r.body,
      ),
    );
  }
}
