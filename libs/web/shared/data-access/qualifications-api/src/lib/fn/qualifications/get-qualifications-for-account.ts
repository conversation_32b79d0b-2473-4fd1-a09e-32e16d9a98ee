/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { QualificationResponse } from '../../models/qualification-response';

export interface GetQualificationsForAccount$Params {}

export function getQualificationsForAccount(
  http: HttpClient,
  rootUrl: string,
  params?: GetQualificationsForAccount$Params,
  context?: HttpContext,
): Observable<StrictHttpResponse<QualificationResponse>> {
  const rb = new RequestBuilder(
    rootUrl,
    getQualificationsForAccount.PATH,
    'get',
  );
  if (params) {
  }

  return http
    .request(
      rb.build({ responseType: 'json', accept: 'application/json', context }),
    )
    .pipe(
      filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return r as StrictHttpResponse<QualificationResponse>;
      }),
    );
}

getQualificationsForAccount.PATH = '/qualifications';
