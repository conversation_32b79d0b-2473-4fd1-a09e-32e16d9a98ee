import { ShiftServiceEnvironment } from './environment.model';
import { getEnvironmentDomain } from './environment.utils';

const DEV_DOMAIN = 'share2act-test.io';

const DOMAIN = getEnvironmentDomain(DEV_DOMAIN);

export const BASE_ENVIRONMENT: ShiftServiceEnvironment = {
  production: true,
  apiUrl: `https://api.${DOMAIN}/v1/`,
  iconUrl: `https://icons.${DOMAIN}/v1/`,
  translationUrl: `https://translations.${DOMAIN}/v1/`,
  domainUrl: `${location.origin}/`,
  styleUrl: `https://assets.${DOMAIN}/v14/styles/`,
  imageUrl: `https://assets.${DOMAIN}/images/`,
  instancesApiUrl: `https://shifts.${DOMAIN}`,
  templatesApiUrl: `https://shifts.${DOMAIN}`,
  equipmentApiUrl: `https://shifts.${DOMAIN}`,
  sentryDsn:
    'https://<EMAIL>/67',
};
