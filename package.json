{"name": "shift-management", "version": "0.0.0", "private": true, "license": "MIT", "scripts": {"############################## BUILD ##############################": "", "build": "nx build", "build:all": "nx run-many --target=build --all", "build:api-shifts": "nx run-many --target=build --projects=app-api-instances,app-api-templates,infrastructure-api-shifts --configuration=$BUILD_CONFIGURATION", "build:api-equipment": "nx run-many --target=build --projects=account-upsert,app-api-equipment,infrastructure-api-equipment,equipment-bootstrap-trigger,line-upsert,line-delete,plant-upsert --configuration=$BUILD_CONFIGURATION", "build:web": "nx build web --configuration=$BUILD_CONFIGURATION", "build:web:flash": "nx build web --configuration=production-flash", "build:infra:all": "nx run-many --target=build --projects=infrastructure-*", "build:infra:shifts": "nx run infrastructure-api-shifts:build", "build:infra:equipment": "nx run infrastructure-api-equipment:build", "build:infra:migration": "nx run infrastructure-shifts-migration:build", "build:infra:kms": "nx run infrastructure-kms-cmk:build", "############################## TEST ##############################": "", "test:all": "nx run-many --all --target=test", "test:all:coverage": "nx run-many --all --target=test --ci --code-coverage", "e2e:all": "nx run-many --all --target=e2e", "############################## LINT ##############################": "", "lint:all": "nx run-many --all --target=lint", "lint:fix": "nx run-many --all --target=lint --fix --skip-nx-cache && pnpm run lint:fix:format", "lint:fix:format": "pnpm npx nx format:write", "lint:format": "pnpm npx nx format:check", "lint:style": "pnpm exec stylelint \"(apps|libs)/**/src/**/*.(sa|sc|c)ss\"", "############################## SERVE ##############################": "", "serve:web": "nx run serve", "serve:web:flash": "nx run serve --configuration development-flash-local", "serve:api": "nx run-many --target=serve --projects=app-api-*", "export:swagger": "nx run-many --target=gen-swagger --all --configuration=export --skip-nx-cache", "generate:clients": "nx run-many --target=gen-client --all --skip-nx-cache", "############################## CI ##############################": "", "ci:test": "pnpm run test:all:coverage --configuration=$BUILD_CONFIGURATION", "ci:lint": "pnpm run lint:all --configuration=$BUILD_CONFIGURATION", "ci:lint:format": "pnpm run lint:format", "ci:lint:clients": "nx run-many --all --target=lint-client", "ci:build": "pnpm run build:all --configuration=$BUILD_CONFIGURATION", "ci:build:flash": "pnpm run build:web:flash", "sentry:inject": "sentry-cli sourcemaps inject dist/apps/", "sentry:upload:api": "sentry-cli sourcemaps upload --project=$SENTRY_BACKEND_PROJECT dist/apps/api", "sentry:upload:backend": "sentry-cli sourcemaps upload --project=$SENTRY_BACKEND_PROJECT dist/apps/backend", "sentry:upload:web": "sentry-cli sourcemaps upload --project=$SENTRY_WEB_PROJECT dist/apps/web", "sentry:upload-source-maps": "pnpm run sentry:upload:api && pnpm run sentry:upload:backend && pnpm run sentry:upload:web", "ci:deploy": "pnpm run build:all && pnpm run sentry:inject && pnpm run deploy:cdk:kms && pnpm run deploy:api && pnpm run deploy:equipment && pnpm run deploy:web --configuration=$BUILD_CONFIGURATION", "############################## DEPLOY ##############################": "", "deploy": "pnpm run deploy:api && pnpm run deploy:equipment && pnpm run deploy:web:local", "postdeploy": "./infrastructure/scripts/update-api-stage.sh", "---------------------- DEPLOY:Equipment ---------------------------": "", "deploy:equipment": "pnpm run bundle:equipment && pnpm run deploy:cdk:equipment && pnpm run postdeploy", "bundle:equipment": "nx run-many --target=bundle --projects=account-upsert,equipment-bootstrap-trigger,app-api-equipment,equipment-tree-bootstrap-response,line-upsert,line-delete,plant-upsert --configuration=$BUILD_CONFIGURATION --skip-nx-cache", "deploy:cdk:equipment": "pnpm run build:infra:equipment && npx cdk deploy --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/equipment/main.js", "synth:cdk:equipment": "pnpm run build:infra:equipment && npx cdk synth --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/equipment/main.js", "---------------------- DEPLOY:SHIFT ---------------------------": "", "deploy:api": "pnpm run bundle:api && pnpm run deploy:cdk:api && pnpm run postdeploy", "bundle:api": "nx run-many --target=bundle --projects=app-api-*,instance-*,active-template-*,template-*,shift-*,future-instance-*,qualification-cron-job --configuration=$BUILD_CONFIGURATION", "deploy:cdk:api": "pnpm run build:infra:shifts && npx cdk deploy --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/shifts/main.js", "synth:cdk:api": "pnpm run build:infra:shifts && npx cdk synth --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/shifts/main.js", "deploy:cdk:migration": "pnpm run build:infra:migration && npx cdk deploy --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/migration/main.js", "synth:cdk:migration": "pnpm run build:infra:migration && npx cdk synth --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/migration/main.js", "destroy:cdk:migration": "npx cdk destroy s2a-shift-service-migration --context stage=$AWS_STAGE --app dist/infrastructure/migration/main.js", "deploy:cdk:kms": "pnpm run build:infra:kms && npx cdk deploy --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/kms-cmk/main.js", "synth:cdk:kms": "pnpm run build:infra:kms && npx cdk synth --require-approval=never --context stage=$AWS_STAGE --app dist/infrastructure/kms-cmk/main.js", "destroy:cdk:kms": "npx cdk destroy s2a-shift-service-kms --context stage=$AWS_STAGE --app dist/infrastructure/kms-cmk/main.js", "---------------------- DEPLOY:Web ---------------------------": "", "deploy:web:local": "nx run-many --target=deploy-local --projects=web --configuration=$BUILD_CONFIGURATION", "deploy:web": "nx run-many --target=deploy --projects=web --configuration=$BUILD_CONFIGURATION", "deploy:web:flash": "flash-upload-s2a-bundle ./dist/apps/web/shift-management-flash/ web-components/s2a/s2a-wc-shift-management/", "############################## AFFECTED ##############################": "", "affected:build": "nx affected:build", "affected:lint": "nx affected:lint --max-warning=0", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:test:coverage": "nx affected:test --ci --code-coverage", "############################## OTHER ##############################": "", "prepare": "husky || true", "preinstall": "npx only-allow pnpm", "postinstall": "node ./decorate-angular-cli.js", "s2a-login": "s2a login -s test", "s2a-login-dev": "s2a login"}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.0.0 < 11.0.0"}, "imports": {"#eslint-base-config": "./eslint.config.js", "#eslint-angular-config": "./eslint.angular.config.js"}, "dependencies": {"@angular/animations": "^19.1.4", "@angular/cdk": "^19.1.2", "@angular/common": "^19.1.4", "@angular/compiler": "^19.1.4", "@angular/core": "^19.1.4", "@angular/elements": "^19.1.4", "@angular/forms": "^19.1.4", "@angular/material": "^19.1.2", "@angular/material-date-fns-adapter": "^19.1.2", "@angular/platform-browser": "^19.1.4", "@angular/platform-browser-dynamic": "^19.1.4", "@angular/router": "^19.1.4", "@aws-lambda-powertools/logger": "^2.14.0", "@codegenie/serverless-express": "^4.16.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/platform-express": "^10.4.15", "@nestjs/swagger": "^7.4.2", "@ngrx/component": "^19.0.0", "@ngrx/component-store": "^19.0.0", "@ngrx/effects": "^19.0.0", "@ngrx/entity": "^19.0.0", "@ngrx/operators": "^19.0.0", "@ngrx/router-store": "^19.0.0", "@ngrx/store": "^19.0.0", "@s2a-core/ng-core": "^9.1.0", "@s2a-core/ng-flash": "^9.1.0", "@s2a-shared-components/place-holder": "^7.0.0", "@s2a-shared-compositions/collection-card": "^7.0.0", "@s2a/core": "^9.1.0", "@s2a/isc-events-v3": "==35.15.0-b2", "@s2a/ng-sentry": "^5.0.1", "@s2a/sentry-helper": "^3.3.0", "@sentry/angular": "^8.52.1", "@sentry/browser": "^8.52.1", "@sentry/types": "^8.52.1", "@types/uuid": "^9.0.8", "@vendia/serverless-express": "^4.12.6", "axios": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "ngx-date-fns": "^10.0.1", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "tslib": "^2.8.1", "uuid": "^9.0.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.0", "@angular-devkit/build-angular": "^19.1.5", "@angular-devkit/core": "^19.1.5", "@angular-devkit/schematics": "^19.1.5", "@angular-eslint/builder": "^19.0.2", "@angular-eslint/eslint-plugin": "^19.0.2", "@angular-eslint/eslint-plugin-template": "^19.0.2", "@angular-eslint/schematics": "^19.0.2", "@angular-eslint/template-parser": "^19.0.2", "@angular/cli": "~19.1.0", "@angular/compiler-cli": "^19.1.4", "@angular/language-service": "^19.1.4", "@aws-sdk/client-cloudformation": "^3.738.0", "@aws-sdk/client-dynamodb": "^3.738.0", "@aws-sdk/client-eventbridge": "^3.738.0", "@aws-sdk/client-lambda": "^3.738.0", "@aws-sdk/client-s3": "^3.738.0", "@aws-sdk/client-scheduler": "^3.738.0", "@aws-sdk/client-secrets-manager": "^3.738.0", "@aws-sdk/client-sfn": "^3.738.0", "@aws-sdk/client-sqs": "^3.738.0", "@aws-sdk/lib-dynamodb": "^3.738.0", "@aws-sdk/s3-request-presigner": "^3.738.0", "@aws-sdk/util-dynamodb": "^3.738.0", "@babel/core": "^7.26.7", "@babel/preset-env": "^7.26.7", "@commitlint/config-conventional": "^19.6.0", "@commitlint/config-nx-scopes": "^19.5.0", "@date-fns/utc": "^1.2.0", "@eslint/config-inspector": "^1.0.0", "@flash/cicd-cli": "^0.1.1", "@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@ngrx/eslint-plugin": "19.0.0", "@ngrx/schematics": "^19.0.0", "@ngrx/store-devtools": "^19.0.0", "@nx/angular": "20.4.0", "@nx/cypress": "20.4.0", "@nx/devkit": "20.4.0", "@nx/esbuild": "20.4.0", "@nx/eslint": "20.4.0", "@nx/eslint-plugin": "20.4.0", "@nx/jest": "20.4.0", "@nx/js": "20.4.0", "@nx/nest": "20.4.0", "@nx/node": "20.4.0", "@nx/plugin": "20.4.0", "@nx/web": "20.4.0", "@nx/webpack": "20.4.0", "@nx/workspace": "20.4.0", "@s2a/cli": "^1.8.1", "@s2a/commitlint-config": "^2.0.0", "@s2a/eslint-config-jest": "^4.0.0", "@s2a/eslint-config-ng": "^4.0.0", "@s2a/eslint-config-ngrx": "^4.0.0", "@s2a/eslint-config-prettier": "^3.0.0", "@s2a/eslint-config-typescript": "^5.0.0", "@s2a/eslint-utils": "^3.0.0", "@s2a/prettier-config": "^3.0.0", "@s2a/stylelint-config": "^4.0.0", "@schematics/angular": "^19.1.5", "@smithy/util-stream": "^3.3.4", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/aws-lambda": "^8.10.147", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/minimist": "^1.2.5", "@types/node": "^20.17.16", "@types/uuid": "^9.0.8", "aws-cdk": "^2.177.0", "aws-cdk-lib": "^2.177.0", "aws-lambda": "^1.0.7", "aws-sdk-client-mock": "^3.1.0", "aws-sdk-client-mock-jest": "^3.1.0", "babel-jest": "^29.7.0", "constructs": "^10.4.2", "conventional-changelog-conventionalcommits": "^8.0.0", "copy-webpack-plugin": "^12.0.2", "cypress": "^13.17.0", "dotenv-flow": "^4.1.0", "esbuild": "~0.19.12", "eslint": "^9.19.0", "eslint-plugin-cypress": "^3.6.0", "expect": "^29.7.0", "express": "^4.21.2", "filemanager-webpack-plugin": "^8.0.0", "generate-package-json-webpack-plugin": "^2.6.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-fail-on-console": "^3.3.1", "jest-junit": "^16.0.0", "jest-marbles": "^3.1.0", "jest-preset-angular": "^14.5.1", "lint-staged": "^15.4.3", "minimist": "^1.2.8", "ng-openapi-gen": "^0.52.0", "nx": "20.4.0", "prettier": "^3.4.2", "raw-loader": "^4.0.2", "rxjs-spy": "^8.0.2", "simple-git": "^3.27.0", "source-map-support": "^0.5.21", "stylelint": "^16.14.1", "stylelint-prettier": "^5.0.3", "stylelint-use-nesting": "^6.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "~5.7.3"}, "overrides": {"chokidar": "4.0.3"}}