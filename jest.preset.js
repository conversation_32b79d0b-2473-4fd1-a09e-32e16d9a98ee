const dotenv = require('dotenv-flow');
const nxPreset = require('@nx/jest/preset').default;

dotenv.config({ silent: true });

const esModules = [
  '@s2a',
  '@datorama',
  '@s2a-core',
  '@s2a-core-akita',
  'lodash-es',
].join('|');

/**
 * @type {import('@jest/types').Config.InitialOptions}
 */
module.exports = {
  ...nxPreset,
  verbose: true,
  coverageReporters: ['clover', 'text'],
  collectCoverage: true,
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{js,ts,jsx,tsx}',
    '!**/node_modules/**',
    '!**/*.d.ts',
  ],
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: './tsconfig.spec.json',
      stringifyContentPathRegex: '\\.(html|svg|scss\\?sourceasset)$',
    },
  },
  reporters: [
    'default',
    process.env.CI
      ? [
          'jest-junit',
          {
            uniqueOutputName: 'true',
            outputDirectory: 'reports/junit',
          },
        ]
      : undefined,
  ].filter(Boolean),
  moduleNameMapper: {
    '^(.*?\\.scss)\\?sourceasset$': '$1',
    '\\.scss$': 'identity-obj-proxy',
    '\\.scss\\?sourceasset$': '__mocks__/styleMock.js',
    '^broadcast-channel$': '__mocks__/broadcast-channel.js',
  },
  transform: {
    '^.+\\.js$': ['babel-jest', { rootMode: 'upward' }],
  },
  transformIgnorePatterns: [
    `node_modules/(?!(?:.pnpm/)?(${esModules}|.*\\.mjs$))`,
  ],
  setupFilesAfterEnv: ['./jest.setup.ts'],
};
