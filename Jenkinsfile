#!/usr/bin/env groovy
@Library('performance/jenkins') _
@Library('syskron/aws@feature/plotting')
import com.syskronx.aws.Functions
import com.syskronx.aws.ReadyKit;
import com.syskronx.aws.Sbom;

def fn = new Functions()

def branchCredentialsIdMap = [
  'master': 's2a_aws_jenkins_dev',
  'test': 's2a_aws_jenkins_test',
  'prod': 's2a_aws_jenkins_prod',
]

def awsStageBranchMap = [
  "master": "dev",
  "test": "test",
  "prod": "prod"
]

def buildConfigurationBranchMap = [
  "master": "development",
  "test": "production",
  "prod": "production"
]

pipeline {
  agent {
    dockerfile {
      label 'linux'
      args '-v "/var/run/docker.sock:/var/run/docker.sock:rw"'
      reuseNode true
    }
  }
  options {
    timestamps()
    timeout(time: 2, unit: 'HOURS')
    disableConcurrentBuilds()
  }
  environment {
    AWS_DEFAULT_REGION = "eu-central-1"
    AWS_CREDENTIALS_ID = branchCredentialsIdMap.get(env.BRANCH_NAME, 's2a_aws_jenkins_dev')
    AWS_STAGE = awsStageBranchMap.get(env.BRANCH_NAME, "dev")
    BUILD_CONFIGURATION = buildConfigurationBranchMap.get(env.BRANCH_NAME, "development")
    SENTRY_ORG = "sentry"
    SENTRY_WEB_PROJECT = "shift-service-web"
    SENTRY_BACKEND_PROJECT = "shift-service-api"
    SENTRY_URL = "https://sentry.tools.krones.digital"
    SENTRY_AUTH_TOKEN = credentials("sentry-bitsplease-test-auth-token")
  }
  stages {
    stage('Install') {
      steps {
        script {
          withNPM(npmrcConfig: "npmrc-global") {
            sh 'pnpm install'
            sh 'printenv | sort'
            sh 'pnpm npx nx --version'
            sh 'cp .npmrc /home/<USER>'
          }
        }
      }
    }

    stage('Pipeline') {
      parallel {
        stage('Build') {
          steps {
            sh "pnpm run ci:build"
          }
        }
        stage('Build for FLASH') {
          when {
            anyOf {
              branch 'master';
              branch 'test';
              branch 'prod';
            }
          }
          steps {
            sh "pnpm run ci:build:flash"
          }
        }
        stage('Lint Format') {
          steps {
            sh "pnpm run ci:lint:format"
          }
        }
        stage('Lint') {
          steps {
            sh "pnpm run ci:lint"
          }
        }
        stage('Lint Stylesheets') {
          steps {
            sh "pnpm run lint:style"
          }
        }
        stage('Lint Client') {
          steps {
            sh "pnpm run ci:lint:clients"
          }
        }
        stage('Test') {
          steps {
            sh "pnpm run ci:test"
          }
          post {
            always {
              junit testResults: 'reports/junit/**/*.xml'
              recordCoverage(tools: [[parser: 'CLOVER', pattern: 'reports/coverage/**/*.xml']])
              archiveArtifacts artifacts: 'reports/coverage/**/*.xml', onlyIfSuccessful: true
              withCredentials([usernamePassword(credentialsId: 'S2A_Jenkins', passwordVariable: 'bitbucket_pass', usernameVariable: 'bitbucket_user')]) {
                  publishCoverageToBitbucket steps: this, reportType: 'clover', path: 'reports/coverage/**/*.xml', user: "${bitbucket_user}", pass: "${bitbucket_pass}"
              }
            }
          }
        }
      }
    }

    stage('Deploy') {
      when {
        anyOf {
          branch 'master';
          branch 'test';
          branch 'prod';
        }
      }
      steps {
        withNPM(npmrcConfig: "npmrc-global") {
          withCredentials([
            [
              $class: 'UsernamePasswordMultiBinding',
              credentialsId: "${env.AWS_CREDENTIALS_ID}",
              usernameVariable: 'AWS_ACCESS_KEY_ID',
              passwordVariable: 'AWS_SECRET_ACCESS_KEY'
            ]
          ]) {
            sh "echo ${AWS_STAGE}"
            sh "pnpm run ci:deploy"
          }
        }
      }
    }
    stage('Deploy for FLASH') {
      when {
        anyOf {
          branch 'master';
          branch 'test';
          branch 'prod';
        }
      }
      steps {
        withNPM(npmrcConfig: "npmrc-global") {
          withCredentials([
            [
              $class: 'UsernamePasswordMultiBinding',
              credentialsId: "flash_limited_aws_jenkins_${env.AWS_STAGE}",
              usernameVariable: 'AWS_ACCESS_KEY_ID',
              passwordVariable: 'AWS_SECRET_ACCESS_KEY'
            ]
          ]) {
            sh "pnpm run deploy:web:flash"
          }
        }
      }
    }
    stage('Sentry Upload Source Maps') {
      when {
        anyOf {
          branch 'master'; branch 'test'; branch 'prod';
        }
      }
      steps {
        catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
          sh "pnpm run sentry:upload-source-maps"
        }
      }
    }
    stage('Create SBOM') {
      when {
        anyOf {
          branch 'master'; branch 'test'; branch 'prod';
        }
      }

      steps {
        script {
          def commitHash = sh (script: "git log -n 1 --pretty=format:'%H'", returnStdout: true)

          new Sbom(this).createSbom([
            sbomVersion: commitHash,
            sbomName: 'shift-service',
            sbomPublisher: 'E18 - Analytics',
            s3Path: 's2a_perf/shift-service',
            awsStage: ReadyKit.getAwsStage(env.BRANCH_NAME),
          ])
        }
      }

      post {
        failure {
          echo "Skip SBOM for ${env.BRANCH_NAME}"
        }
      }
    }
  }
  post {
    always {
      cleanWs()
    }
  }
}
