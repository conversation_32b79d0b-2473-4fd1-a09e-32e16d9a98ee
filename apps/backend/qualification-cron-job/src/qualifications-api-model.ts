import { Qualification } from '@shift-management/api/qualifications';
/**
 * OAuth token response from third-party API
 */
export interface OAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

/**
 * Text description for a qualification in different locales
 */
export interface QualificationText {
  locale: string;
  description: string;
}

/**
 * Qualification data structure as received from third-party API
 */
export interface QualificationAPI {
  qualificationID: string;
  description: string;
  texts: QualificationText[];
}

/**
 * Customer qualifications data structure from third-party API
 */
export interface CustomerQualifications {
  Customer: string;
  value: QualificationAPI[];
}

/**
 * Complete response data structure from third-party qualifications API
 */
export interface QualificationsResponseData {
  Customers: CustomerQualifications[];
}

/**
 * Internal representation of account qualifications after transformation
 */
export interface AccountQualifications {
  account: string;
  value: Qualification[];
}

/**
 * AWS Secrets Manager secret value structure
 */
export interface SecretValue {
  client_id: string;
  client_secret: string;
}
