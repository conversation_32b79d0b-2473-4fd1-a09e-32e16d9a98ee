export default {
  displayName: 'qualification-cron-job',
  preset: '../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../reports/coverage/apps/backend//qualification-cron-job',
};
