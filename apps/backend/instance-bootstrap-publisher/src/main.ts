import { Logger } from '@aws-lambda-powertools/logger';
import { SQSBatchItemFailure, SQSBatchResponse, SQSEvent } from 'aws-lambda';

import {
  InstanceV4,
  Service,
  ShiftInstanceBootstrapClassV4,
  ShiftInstancesBootstrapResponseV4Event,
} from '@s2a/isc-events-v3';
import {
  initSentryLambda,
  setSentryTags,
  wrapSentryHandler,
} from '@s2a/sentry-helper';
import {
  ApiInstancesService,
  InstanceDB,
} from '@shift-management/api/instances';
import { DynamoDBClientProvider } from '@shift-management/api/util/dynamodb-client';

const logger = new Logger({
  serviceName: 'shifts-instance-bootstrap-publisher',
});

initSentryLambda();

export const handler = wrapSentryHandler(
  async (event: SQSEvent, _context: unknown): Promise<SQSBatchResponse> => {
    logger.debug('Fetching instances', {
      event,
    });
    // fetch all the corresponding instances of given account-line pair and push into ISC.
    const messages_to_reprocess: SQSBatchItemFailure[] = [];
    const eventbusName = process.env['EVENTBRIDGE_NAME'];
    const dynamoDBClient = new DynamoDBClientProvider();
    const instanceService = new ApiInstancesService(dynamoDBClient);
    for (const record of event.Records) {
      if (eventbusName === undefined) {
        logger.error('Missing EVENTBRIDGE_NAME from env variables');
        messages_to_reprocess.push({ itemIdentifier: record.messageId });
        continue;
      }
      const bodyData = JSON.parse(record.body);
      const equipmentId = bodyData.equipmentId;
      setSentryTags({
        account: bodyData.account,
        equipmentId: bodyData.equipmentId,
      });
      try {
        const instanceDBs = await instanceService.findAllInstancesForLine(
          bodyData.account,
          equipmentId,
        );
        const instancesCount = instanceDBs.length;
        logger.debug('Number of instances to be published for lineId', {
          instancesCount,
          equipmentId,
        });
        await publishToISC(instanceDBs, bodyData.source);
      } catch (error) {
        logger.error('error in finding instances for line, Retrying...', {
          error,
          equipmentId,
        });
        messages_to_reprocess.push({ itemIdentifier: record.messageId });
      }
    }
    return { batchItemFailures: messages_to_reprocess };
  },
);

const publishToISC = async (instances: InstanceDB[], requester: Service) => {
  const responseEvent = new ShiftInstancesBootstrapResponseV4Event();
  responseEvent.setMessage({
    instances: instances.map((instance): InstanceV4 => {
      return {
        instanceClass:
          instance.class as unknown as ShiftInstanceBootstrapClassV4,
        name: instance.name,
        entityType: instance.entityType,
        start: instance.start,
        end: instance.end,
        account: instance.account,
        lineId: instance.lineId,
        instanceId: instance.instanceId,
        templateId: instance.templateId,
        updatedAt: instance.updatedAt,
      };
    }),
  });
  responseEvent.setRequester(requester);
  await responseEvent.publish();
};
