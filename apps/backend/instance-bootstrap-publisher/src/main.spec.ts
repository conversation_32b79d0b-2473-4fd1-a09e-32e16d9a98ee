import { beforeEach } from '@jest/globals';
import { SQSEvent } from 'aws-lambda';

import { InstanceDB } from '@shift-management/api/instances';
import { ClassEnum } from '@shift-management/shared/types';

import { handler } from './main';

const responseEventMock = {
  publish: jest.fn(),
  setMessage: jest.fn(),
  setRequester: jest.fn(),
};
const findAllInstancesForLineMock = jest.fn();

jest.mock('@s2a/isc-events-v3', () => {
  return {
    ShiftInstancesBootstrapResponseV4Event: jest.fn().mockImplementation(() => {
      return responseEventMock;
    }),
    Service: 'shift',
  };
});
jest.mock('@shift-management/api/instances', () => {
  return {
    ApiInstancesService: jest.fn().mockImplementation(() => {
      return {
        findAllInstancesForLine: findAllInstancesForLineMock,
      };
    }),
    Instance: {
      from: (x: unknown) => x,
    },
  };
});

describe('handler', () => {
  const event: SQSEvent = {
    Records: [
      {
        body: JSON.stringify({
          equipmentId: 'e',
          account: 'a',
          source: 's',
        }),
        messageId: '4444',
      },
    ],
  } as unknown as SQSEvent;

  const testInstances: InstanceDB[] = [
    {
      instanceId: '1',
      PK: 'PK',
      SK: 'SK',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      LSI1_SK: 1,
      entityType: 'instance',
      class: ClassEnum.Production,
      updatedAt: 5,
      createdAt: 4,
      lineId: '151',
      account: 'account',
      name: '3',
      start: 1,
      end: 5,
    },
  ];

  beforeEach(() => {
    responseEventMock.setMessage.mockReset();
    responseEventMock.publish.mockReset();
    responseEventMock.setRequester.mockReset();
    findAllInstancesForLineMock.mockReset();
  });

  it('should publish ISC events', async () => {
    // arrange
    findAllInstancesForLineMock.mockReturnValue(Promise.resolve(testInstances));

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await handler(event, {});

    // assert
    expect(responseEventMock.setMessage).toHaveBeenCalledWith({
      instances: [
        {
          account: 'account',
          end: 5,
          entityType: 'instance',
          instanceClass: 'productive',
          instanceId: '1',
          lineId: '151',
          name: '3',
          start: 1,
          templateId: undefined,
          updatedAt: 5,
        },
      ],
    });

    expect(responseEventMock.setRequester).toHaveBeenCalledWith('s');
    expect(responseEventMock.publish).toHaveBeenCalled();
  });

  it('should return failed messageIds', async () => {
    // arrange
    findAllInstancesForLineMock.mockImplementation(() => {
      throw new Error();
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await handler(event, {});

    // assert
    expect(result).toEqual({ batchItemFailures: [{ itemIdentifier: '4444' }] });
  });

  it('should return all messageIds when EVENTBRIDGE_NAME is not defined', async () => {
    // arrange
    delete process.env['EVENTBRIDGE_NAME'];
    findAllInstancesForLineMock.mockImplementation(() =>
      Promise.resolve(testInstances),
    );

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await handler(event, {});

    // assert
    expect(result).toEqual({ batchItemFailures: [{ itemIdentifier: '4444' }] });
  });

  it('should not publish anything when EVENTBRIDGE_NAME is not defined', async () => {
    // arrange
    delete process.env['EVENTBRIDGE_NAME'];
    findAllInstancesForLineMock.mockImplementation(() =>
      Promise.resolve(testInstances),
    );

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await handler(event, {});

    // assert
    expect(responseEventMock.publish).not.toHaveBeenCalled();
  });
});
