import { SQSEvent } from 'aws-lambda';

import { handler } from './main';

const mockPublish = jest.fn();
const mockSetMessage = jest.fn();
const mockSetSource = jest.fn();
jest.mock('@s2a/isc-events-v3', () => {
  return {
    ShiftInstanceDeletedBroadcastV4Event: jest.fn().mockImplementation(() => {
      return {
        publish: mockPublish,
        setMessage: mockSetMessage,
        setSource: mockSetSource,
      };
    }),
    Service: 'shift',
  };
});

describe('handler', () => {
  beforeEach(() => {
    mockPublish.mockReset();
    mockSetMessage.mockReset();
    mockSetSource.mockReset();
  });

  it('should delete an instance', async () => {
    const instanceData = {
      instanceId: '66d80a43-eba5-4150-a47c-fc343ecfad35',
      account: 'readykit-replay',
      lineId: '96c583d6-741c-4751-8cb2-47f0db2b1c18',
      start: 0,
      end: 1,
      class: 'productive',
      name: 'Shift 1',
      entityType: 'instance',
      templateId: 'test-template-id',
    };

    const event = {
      Records: [
        {
          body: JSON.stringify({
            detail: { dynamodb: { OldImage: instanceData } },
          }),
        },
      ],
    };
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await handler(event as SQSEvent, {});

    expect(mockPublish).toHaveBeenCalled();
    expect(mockSetMessage).toHaveBeenCalledWith({
      instanceId: '66d80a43-eba5-4150-a47c-fc343ecfad35',
      account: 'readykit-replay',
      lineId: '96c583d6-741c-4751-8cb2-47f0db2b1c18',
      start: 0,
      end: 1,
      instanceClass: 'productive',
      name: 'Shift 1',
      entityType: 'instance',
      templateId: 'test-template-id',
    });

    expect(result).toEqual({ batchItemFailures: [] });
  });

  it('should publish once and return one messageId', async () => {
    const instanceData = {
      account: 'readykit-replay',
      lineId: '96c583d6-741c-4751-8cb2-47f0db2b1c18',
      instanceId: '66d80a43-eba5-4150-a47c-fc343ecfad35',
    };

    const event = {
      Records: [
        {
          messageId: '1',
          body: JSON.stringify({
            detail: { dynamodb: { OldImage: instanceData } },
          }),
        },
        {
          messageId: '2',
          body: JSON.stringify({
            detail: { dynamodb: { OldImage: instanceData } },
          }),
        },
      ],
    };

    mockSetMessage
      .mockImplementation(() => {
        throw Error('Oops');
      })
      .mockImplementationOnce(() => undefined);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await handler(event as SQSEvent, {});

    expect(mockPublish).toHaveBeenCalledTimes(1);
    expect(result).toEqual({
      batchItemFailures: [
        {
          itemIdentifier: '2',
        },
      ],
    });
  });

  it('should return messageId on malformed dynamodbStreamRecord', async () => {
    const event = {
      Records: [
        {
          messageId: '1',
          body: JSON.stringify({
            detail: {},
          }),
        },
      ],
    };

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await handler(event as SQSEvent, {});

    expect(result).toEqual({
      batchItemFailures: [
        {
          itemIdentifier: '1',
        },
      ],
    });
  });
});
