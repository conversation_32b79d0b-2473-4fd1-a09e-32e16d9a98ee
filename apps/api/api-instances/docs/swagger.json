{"openapi": "3.0.0", "paths": {"/instances/line/{lineId}": {"get": {"operationId": "getInstancesForLine", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "start", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "end", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstanceResponse"}}}}}, "tags": ["instances"]}}, "/instances/line/{lineId}/instance": {"post": {"operationId": "insertInstanceForLine", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInstanceDTO"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Instance"}}}}}, "tags": ["instances"]}}, "/instances/line/{lineId}/instance/{instanceId}": {"delete": {"operationId": "deleteInstanceForLine", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "instanceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["instances"]}, "patch": {"operationId": "updateInstance", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "instanceId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInstanceDTO"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Instance"}}}}}, "tags": ["instances"]}}}, "info": {"title": "shift-management-api-instances", "description": "Shift API Instances", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"Instance": {"type": "object", "properties": {"name": {"type": "string"}, "entityType": {"type": "string"}, "class": {"default": "productive", "enum": ["productive", "changeover", "cleaning", "maintenance", "testProduction", "other"], "type": "string"}, "start": {"type": "number"}, "end": {"type": "number"}, "account": {"type": "string"}, "lineId": {"type": "string"}, "instanceId": {"type": "string"}, "templateId": {"type": "string"}, "qualificationId": {"type": "string"}}, "required": ["name", "entityType", "class", "start", "end", "account", "lineId", "instanceId"]}, "InstanceResponse": {"type": "object", "properties": {"instances": {"type": "array", "items": {"$ref": "#/components/schemas/Instance"}}}, "required": ["instances"]}, "CreateInstanceDTO": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}, "name": {"type": "string"}, "class": {"enum": ["productive", "changeover", "cleaning", "maintenance", "testProduction", "other"], "type": "string"}, "qualificationId": {"type": "string"}}, "required": ["start", "end", "name", "class"]}, "UpdateInstanceDTO": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}, "name": {"type": "string"}, "class": {"enum": ["productive", "changeover", "cleaning", "maintenance", "testProduction", "other"], "type": "string"}, "qualificationId": {"type": "string"}}}}}}