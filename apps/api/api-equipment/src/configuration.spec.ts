import applicationOptions from './configuration';

describe('applicationOptions', () => {
  it('should have the correct serviceName in logger', () => {
    expect(applicationOptions.logger?.serviceName).toBe('shifts-api-equipment');
  });

  it('should match the expected ApplicationOptions structure', () => {
    expect(applicationOptions).toEqual(
      expect.objectContaining({
        logger: expect.objectContaining({
          serviceName: expect.any(String),
        }),
      }),
    );
  });
});
