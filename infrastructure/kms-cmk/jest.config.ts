/* eslint-disable */
export default {
  displayName: 'infrastructure-kms-cmk',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
        isolatedModules: true,
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  collectCoverageFrom: [
    '<rootDir>/**/*.{js,ts,jsx,tsx}',
    '!**/node_modules/**',
    '!**/*.d.ts',
  ],
  coverageDirectory: '../../reports/coverage/infrastructure/kms-cmk',
};
/* eslint-enable */
