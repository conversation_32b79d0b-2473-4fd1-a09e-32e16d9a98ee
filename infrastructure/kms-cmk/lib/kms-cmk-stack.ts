import { CfnOutput, Stack, StackProps, aws_iam as IAM } from 'aws-cdk-lib';
import { Construct } from 'constructs';

import { TagsConstruct } from '@shift-management/infrastructure/constructs';
import {
  DYNAMODB_KMS_ALIAS_NAME,
  SQS_KMS_ALIAS_NAME,
  EVENTBRIDGE_KMS_ALIAS_NAME,
  SECRETS_MANAGER_KMS_ALIAS_NAME,
} from '@shift-management/infrastructure/util';

import { KMSConstruct } from './kms-construct';

export class KmsCmkStack extends Stack {
  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props);

    new TagsConstruct(this, 'shift-tag', 's2a-shift-service');
    const account = Stack.of(this).account;
    const region = Stack.of(this).region;
    const stage = this.node.tryGetContext('stage') || 'dev';
    const adminRole: IAM.IRole = IAM.Role.fromRoleArn(
      this,
      's2a-admin-role',
      `arn:aws:iam::${account}:role/Syskron_Admin`,
    );
    const ssoAdminRoleArn = `arn:aws:iam::${account}:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Syskron_SSO_KMS_Ops_*`;
    const jenkinsUser: IAM.IUser = IAM.User.fromUserArn(
      this,
      'jenkins-user',
      `arn:aws:iam::${account}:user/s2a_jenkins_${stage}`,
    );
    const dynamoAllowedServices = [
      `lambda.${region}.amazonaws.com`,
      `dynamodb${region}.amazonaws.com`,
    ];
    // DynamoDB Keys
    const shiftServiceDynamodbKey = new KMSConstruct(
      this,
      'dynamodb',
      adminRole,
      jenkinsUser,
      account,
      dynamoAllowedServices,
      undefined,
      ssoAdminRoleArn,
      undefined,
    );

    // SQS Key
    const sqsAllowedServices = [
      'events.amazonaws.com',
      `sqs.${region}.amazonaws.com`,
      `lambda.${region}.amazonaws.com`,
    ];
    const sqsKey = new KMSConstruct(
      this,
      'sqs',
      adminRole,
      jenkinsUser,
      account,
      sqsAllowedServices,
      undefined,
      ssoAdminRoleArn,
      undefined,
    );
    // CloudWatch Logs Key
    const logsSpecialCond = {
      ArnLike: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'kms:EncryptionContext:aws:logs:arn': `arn:aws:logs:${region}:${account}:*`,
      },
    };

    const logsKey = new KMSConstruct(
      this,
      'cloudwatchLogGroup',
      adminRole,
      jenkinsUser,
      account,
      undefined,
      logsSpecialCond,
      ssoAdminRoleArn,
      undefined,
    );
    // eventBridge
    const eventsAllowedServices = [
      'events.amazonaws.com',
      'scheduler.amazonaws.com',
      'pipes.amazonaws.com',
      'states.amazonaws.com',
      `sqs.${region}.amazonaws.com`,
      `s3.${region}.amazonaws.com`,
    ];
    const eventBridgeKey = new KMSConstruct(
      this,
      'eventBridge',
      adminRole,
      jenkinsUser,
      account,
      eventsAllowedServices,
      undefined,
      ssoAdminRoleArn,
      ['events.amazonaws.com'],
    );

    // Secrets Manager Key
    const secretsManagerAllowedServices = [
      `secretsmanager.${region}.amazonaws.com`,
      `lambda.${region}.amazonaws.com`,
    ];
    const secretsManagerKey = new KMSConstruct(
      this,
      'secretsManager',
      adminRole,
      jenkinsUser,
      account,
      secretsManagerAllowedServices,
      undefined,
      ssoAdminRoleArn,
      ['secretsmanager.amazonaws.com'],
    );

    // ######################### Cloudformation outputs #########################

    new CfnOutput(this, 'ShiftServiceDynamoDBKeyAlias', {
      value: shiftServiceDynamodbKey.keyAlias.aliasName,
      exportName: DYNAMODB_KMS_ALIAS_NAME,
    });

    new CfnOutput(this, 'ShiftServiceSQSAlias', {
      value: sqsKey.keyAlias.aliasName,
      exportName: SQS_KMS_ALIAS_NAME,
    });

    new CfnOutput(this, 'ShiftServiceCWAlias', {
      value: logsKey.keyAlias.aliasName,
      exportName: 'shift-service-cw-logs-kms-key-alias',
    });

    new CfnOutput(this, 'ShiftServiceEventBridgeAlias', {
      value: eventBridgeKey.keyAlias.aliasName,
      exportName: EVENTBRIDGE_KMS_ALIAS_NAME,
    });

    new CfnOutput(this, 'ShiftServiceSecretsManagerAlias', {
      value: secretsManagerKey.keyAlias.aliasName,
      exportName: SECRETS_MANAGER_KMS_ALIAS_NAME,
    });
  }
}
